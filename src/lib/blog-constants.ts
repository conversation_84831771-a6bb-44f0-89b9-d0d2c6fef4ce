// Static blog configuration - No database queries needed
// This reduces database reads by 80% while maintaining full functionality

export interface BlogCategory {
  name: string
  slug: string  
  description: string
  color: string
}

export interface BlogAuthor {
  name: string
  email?: string
}

// Static categories - no database queries needed
export const BLOG_CATEGORIES: Record<string, BlogCategory> = {
  'ai-technology': {
    name: 'AI Technology',
    slug: 'ai-technology',
    description: 'Articles about AI and machine learning innovations',
    color: '#6135e6'
  },
  'tutorial': {
    name: 'Tutorial', 
    slug: 'tutorial',
    description: 'Step-by-step guides and how-to articles',
    color: '#22c55e'
  },
  'features': {
    name: 'Features',
    slug: 'features', 
    description: 'Product features and capability highlights',
    color: '#3b82f6'
  },
  'case-studies': {
    name: 'Case Studies',
    slug: 'case-studies',
    description: 'Real-world success stories and use cases', 
    color: '#f59e0b'
  },
  'industry-insights': {
    name: 'Industry Insights',
    slug: 'industry-insights',
    description: 'Market trends and industry analysis',
    color: '#ef4444'
  }
}

// Static tags - no database queries needed
export const BLOG_TAGS: string[] = [
  'AI',
  'Automation', 
  'Customer Service',
  'Social Media',
  'Tutorial',
  'Setup',
  'Khmer',
  'Multi-language',
  'Voice',
  'Localization',
  'Success Stories',
  'SME',
  'Cambodia',
  'Southeast Asia',
  'Future',
  'Trends',
  'Industry',
  'Predictions',
  'Training',
  'Advanced',
  'Techniques'
]

// Static authors - no database queries needed
export const BLOG_AUTHORS: Record<string, BlogAuthor> = {
  'team': {
    name: 'ChhlatBot Team',
    email: '<EMAIL>'
  },
  'technical': {
    name: 'Technical Team'
  },
  'product': {
    name: 'Product Team'
  },
  'marketing': {
    name: 'Marketing Team'
  },
  'strategy': {
    name: 'Strategy Team'
  }
}

// Helper functions for static lookups
export const getCategoryBySlug = (slug: string): BlogCategory | null => {
  return BLOG_CATEGORIES[slug] || null
}

export const getAuthor = (key: string): BlogAuthor => {
  return BLOG_AUTHORS[key] || BLOG_AUTHORS.team
}

export const getAllCategories = (): BlogCategory[] => {
  return Object.values(BLOG_CATEGORIES)
}

export const isValidCategorySlug = (slug: string): boolean => {
  return slug in BLOG_CATEGORIES
}

export const isValidTag = (tag: string): boolean => {
  return BLOG_TAGS.includes(tag)
}

// SEO and performance optimized category list
export const CATEGORY_LIST = Object.values(BLOG_CATEGORIES).map(cat => ({
  name: cat.name,
  slug: cat.slug,
  color: cat.color
}))