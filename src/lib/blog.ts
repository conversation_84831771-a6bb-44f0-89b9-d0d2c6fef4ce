import { queryMany, queryOne } from '@/lib/postgres'
import { 
  BLOG_CATEGORIES, 
  BLOG_AUTHORS, 
  getCategoryBySlug, 
  getAuthor,
  type BlogCategory as StaticBlogCategory 
} from '@/lib/blog-constants'

export interface BlogPost {
  id: number
  title: string
  slug: string
  excerpt: string
  featuredImage?: string
  author: string
  category: {
    name: string
    slug: string
    color: string
  }
  tags: string[]
  featured: boolean
  readTime: string
  viewCount: number
  publishedAt: string
}

export interface BlogPostDetail {
  id: number
  title: string
  slug: string
  excerpt: string
  content: string
  featuredImage?: string
  author: {
    name: string
    email?: string
  }
  category: {
    name: string
    slug: string
    color: string
  }
  tags: string[]
  featured: boolean
  meta: {
    title: string
    description: string
  }
  readTime: string
  viewCount: number
  publishedAt: string
}

// BlogCategory is now imported from static constants
export type BlogCategory = StaticBlogCategory & {
  postCount: number
}

export interface PaginationInfo {
  currentPage: number
  totalPages: number
  totalCount: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

// Fetch blog posts with filtering and pagination
export async function getBlogPosts({
  limit = 10,
  page = 1,
  category = null,
  search = null,
  featured = false
}: {
  limit?: number
  page?: number
  category?: string | null
  search?: string | null
  featured?: boolean
} = {}) {
  try {
    const offset = (page - 1) * limit
    
    const posts = await queryMany<{
      id: number
      title: string
      slug: string
      excerpt: string
      featured_image: string | null
      author_key: string
      category_slug: string
      tags: string[]
      featured: boolean
      read_time_minutes: number
      view_count: number
      published_at: string
      total_count: string
    }>('SELECT * FROM public.get_blog_posts($1, $2, $3, $4, $5)', [
      limit,
      offset,
      category,
      search,
      featured
    ])

    const totalCount = posts.length > 0 ? parseInt(posts[0].total_count) : 0
    const totalPages = Math.ceil(totalCount / limit)

    return {
      posts: posts.map(post => {
        const category = getCategoryBySlug(post.category_slug)
        const author = getAuthor(post.author_key)
        
        return {
          id: post.id,
          title: post.title,
          slug: post.slug,
          excerpt: post.excerpt,
          featuredImage: post.featured_image || undefined,
          author: author.name,
          category: {
            name: category?.name || 'Unknown',
            slug: post.category_slug,
            color: category?.color || '#6135e6'
          },
          tags: post.tags || [],
          featured: post.featured,
          readTime: `${post.read_time_minutes} min read`,
          viewCount: post.view_count,
          publishedAt: post.published_at
        }
      }),
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      }
    }
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    throw error
  }
}

// Fetch single blog post by slug
export async function getBlogPostBySlug(slug: string) {
  try {
    const post = await queryOne<{
      id: number
      title: string
      slug: string
      excerpt: string
      content: string
      featured_image: string | null
      author_key: string
      category_slug: string
      tags: string[]
      featured: boolean
      meta_title: string | null
      meta_description: string | null
      read_time_minutes: number
      view_count: number
      published_at: string
    }>('SELECT * FROM public.get_blog_post_by_slug($1)', [slug])

    if (!post) {
      return null
    }

    const relatedPosts = await queryMany<{
      id: number
      title: string
      slug: string
      excerpt: string
      featured_image: string | null
      author_key: string
      category_slug: string
      read_time_minutes: number
      published_at: string
    }>('SELECT * FROM public.get_related_posts($1, $2)', [post.id, 3])

    const category = getCategoryBySlug(post.category_slug)
    const author = getAuthor(post.author_key)

    return {
      post: {
        id: post.id,
        title: post.title,
        slug: post.slug,
        excerpt: post.excerpt,
        content: post.content,
        featuredImage: post.featured_image || undefined,
        author: {
          name: author.name,
          email: author.email
        },
        category: {
          name: category?.name || 'Unknown',
          slug: post.category_slug,
          color: category?.color || '#6135e6'
        },
        tags: post.tags || [],
        featured: post.featured,
        meta: {
          title: post.meta_title || post.title,
          description: post.meta_description || post.excerpt
        },
        readTime: `${post.read_time_minutes} min read`,
        viewCount: post.view_count,
        publishedAt: post.published_at
      },
      relatedPosts: relatedPosts.map(related => {
        const relatedCategory = getCategoryBySlug(related.category_slug)
        const relatedAuthor = getAuthor(related.author_key)
        
        return {
          id: related.id,
          title: related.title,
          slug: related.slug,
          excerpt: related.excerpt,
          featuredImage: related.featured_image || undefined,
          author: relatedAuthor.name,
          category: {
            name: relatedCategory?.name || 'Unknown',
            slug: related.category_slug,
            color: relatedCategory?.color || '#6135e6'
          },
          tags: [], // Tags not needed for related posts
          featured: false,
          readTime: `${related.read_time_minutes} min read`,
          viewCount: 0, // Don't show view count for related posts
          publishedAt: related.published_at
        }
      })
    }
  } catch (error) {
    console.error('Error fetching blog post by slug:', error)
    throw error
  }
}

// Fetch blog categories with post counts (uses static categories + database counts)
export async function getBlogCategories(): Promise<BlogCategory[]> {
  try {
    const categoryCounts = await queryMany<{
      category_slug: string
      post_count: number
    }>(`
      SELECT 
        category_slug,
        COUNT(*) as post_count
      FROM public.blog_posts 
      WHERE status = 'published'
      GROUP BY category_slug
    `)

    const countMap = new Map(
      categoryCounts.map(c => [c.category_slug, parseInt(c.post_count.toString())])
    )

    return Object.values(BLOG_CATEGORIES).map(cat => ({
      ...cat,
      postCount: countMap.get(cat.slug) || 0
    }))
  } catch (error) {
    console.error('Error fetching blog categories:', error)
    throw error
  }
}

// Get blog statistics
export async function getBlogStats() {
  try {
    const stats = await queryOne<{
      published_posts: number
      draft_posts: number
      active_categories: number
      active_tags: number
      total_views: number
      avg_read_time: number
    }>('SELECT * FROM public.blog_stats')

    return {
      publishedPosts: stats?.published_posts || 0,
      draftPosts: stats?.draft_posts || 0,
      activeCategories: stats?.active_categories || 0,
      activeTags: stats?.active_tags || 0,
      totalViews: stats?.total_views || 0,
      avgReadTime: Math.round(stats?.avg_read_time || 0)
    }
  } catch (error) {
    console.error('Error fetching blog stats:', error)
    throw error
  }
}