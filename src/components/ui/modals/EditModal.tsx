'use client'

import { useRef, useEffect, useState, useMemo } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'

interface EditModalProps {
  editingItem: {
    id: number
    field: 'question' | 'answer'
    value: string
  } | null
  hasFocusedInput: boolean
  onValueChange: (value: string) => void
  onInputFocus: () => void
  onSave: () => void
  onClose: () => void
}

export default function EditModal({ 
  editingItem, 
  hasFocusedInput, 
  onValueChange, 
  onInputFocus, 
  onSave, 
  onClose 
}: EditModalProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const modalRef = useRef<HTMLDivElement>(null)
  
  // Local state for textarea to prevent re-renders on every keystroke
  const [localValue, setLocalValue] = useState('')

  // Update local value when editingItem changes
  useEffect(() => {
    if (editingItem) {
      setLocalValue(editingItem.value)
    }
  }, [editingItem?.id, editingItem?.field])

  // Memoize mobile detection
  const isMobileDevice = useMemo(() => {
    if (typeof window === 'undefined' || typeof navigator === 'undefined') return false
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
           ('ontouchstart' in window && window.innerWidth <= 1024)
  }, [])

  // Auto-focus and position cursor at end when modal opens (only once)
  useEffect(() => {
    if (editingItem && textareaRef.current && !hasFocusedInput) {
      const textarea = textareaRef.current
      // Focus the textarea
      textarea.focus()
      // Position cursor at the end only on initial open
      const length = textarea.value.length
      textarea.setSelectionRange(length, length)
      // Trigger focus callback
      onInputFocus()
    }
  }, [editingItem, onInputFocus, hasFocusedInput])


  // Position modal bottom at vertical half-screen on mobile
  useEffect(() => {
    if (modalRef.current && editingItem && isMobileDevice) {
      // Position modal so its bottom edge is at 50vh from top
      modalRef.current.style.setProperty('top', '50vh')
      modalRef.current.style.setProperty('bottom', 'auto')
      modalRef.current.style.setProperty('transform', 'translateY(-120%)')
    }
  }, [editingItem])


  // Handle escape key to close modal without saving
  useEffect(() => {
    if (editingItem) {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          event.preventDefault()
          onClose() // Close without saving
        }
      }
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [editingItem, onClose])

  if (!editingItem) return null

  return (
    <div
      className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50 p-4"
    >
      <div
        ref={modalRef}
        className={`relative ${themeConfig.card} rounded-2xl p-6 w-full max-w-lg mx-4 border-2 ${themeConfig.border} overflow-hidden shadow-xl`}
        onClick={(e: React.MouseEvent) => e.stopPropagation()}
      >
        <div className="relative z-10" data-modal-content>
          {/* Close button (X) */}
          <button
            className={`absolute top-0 right-0 p-1.5 border ${themeConfig.border} rounded-full ${themeConfig.interactive} hover:bg-jade-purple ${themeConfig.textSecondary} hover:text-white transition-colors z-20`}
            onClick={onClose}
            aria-label="Close"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          <h3 className={`text-xl ${themeConfig.text} font-bold mb-4 font-title text-center`}>
            {editingItem.field === 'question' ? t('kb_edit_question') : t('edit_reply')}
          </h3>
          <textarea
            ref={textareaRef}
            value={localValue}
            onChange={(e) => {
              setLocalValue(e.target.value)
              onValueChange(e.target.value)
            }}
            className={`w-full px-4 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} placeholder-gray-400 focus:outline-none min-h-[150px] mb-4 ${themeConfig.borderHover} font-body`}
            placeholder={editingItem.field === 'question' ? t('enter_question') : t('enter_reply')}
          />
          <button
            onClick={onSave}
            className="bg-jade-purple-dark text-white hover:bg-jade-purple hover:shadow-md hover:bg-jade-purple transition-all duration-200 px-6 py-2 rounded-lg font-medium w-full border border-jade-purple/75 font-body"
          >
            {t('done')}
          </button>
        </div>
      </div>
    </div>
  )
}