'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { FaFacebook, FaInstagram, FaTiktok, FaTelegram, FaYoutube } from 'react-icons/fa'
import { useLanguage } from '@/context/LanguageContext'

export default function Footer() {
  const currentYear = new Date().getFullYear()
  const { t } = useLanguage()

  const footerLinks = [
    {
      titleKey: 'footer_product',
      links: [
        { nameKey: 'footer_features', href: '#features' },
        { nameKey: 'footer_pricing', href: '#pricing' },
        { nameKey: 'footer_how_it_works', href: '#how-it-works' },
        { nameKey: 'footer_faq', href: '#faq' },
      ]
    },
    {
      titleKey: 'footer_company',
      links: [
        { nameKey: 'footer_blog', href: 'learn.chhlatbot/blog' },
        { nameKey: 'footer_docs', href: 'learn.chhlatbot/docs' },
      ]
    },
  ]

  const socialLinks = [
    { 
      name: 'Facebook', 
      icon: <FaFacebook className="text-lg" />, 
      href: 'https://www.facebook.com/chhlatbot'  // Replace with actual page URL
    },
    { 
      name: 'TikTok', 
      icon: <FaTiktok className="text-lg" />, 
      href: 'https://www.tiktok.com/@chhlatbot'  // Replace with actual TikTok username
    },
    { 
      name: 'Instagram', 
      icon: <FaInstagram className="text-lg" />, 
      href: 'https://www.instagram.com/chhlatbot'  // Replace with actual Instagram username
    },
    { 
      name: 'YouTube', 
      icon: <FaYoutube className="text-lg" />, 
      href: 'https://www.youtube.com/@chhlatbot'  // Replace with actual YouTube channel
    },
    { 
      name: 'Telegram', 
      icon: <FaTelegram className="text-lg" />, 
      href: 'https://t.me/chhlatbot'  // Replace with actual Telegram channel/bot
    },
  ]

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <footer className="text-white relative overflow-hidden mt-16">

      {/* Modern clean footer */}
      <div className="container mx-auto px-4 py-12 relative z-10">
        <div className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-8 border border-white/20 hover:border-white/30 transition-all duration-300 overflow-hidden">
          {/* Simple background overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/3 to-transparent rounded-2xl"></div>

          {/* Content */}
          <div className="relative z-10">
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
              <div className="col-span-2">
                <div
                  className="mb-6 cursor-pointer group"
                  onClick={scrollToTop}
                >
                  <Image
                    src="/images/white_tran_logo.svg"
                    alt="Chhlat Bot"
                    width={120}
                    height={40}
                    className="h-10 w-auto transition-transform duration-300 group-hover:scale-105"
                  />
                </div>
                <p className="font-body mb-8 text-zinc-300 text-sm sm:text-base leading-relaxed">
                  {t('footer_description')}
                </p>
                <div className="flex space-x-3">
                  {socialLinks.map((social, index) => (
                    <motion.a
                      key={index}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-10 h-10 sm:w-12 sm:h-12 border border-white/20 rounded-xl flex items-center justify-center text-zinc-300 hover:text-white hover:bg-jade-purple hover:border-jade-purple transition-all duration-300"
                      whileHover={{ y: -1}}
                      aria-label={social.name}
                    >
                      {social.icon}
                    </motion.a>
                  ))}
                </div>
              </div>

              {footerLinks.map((section, index) => (
                <div key={index}>
                  <h4 className="text-lg font-semibold mb-6 font-title text-white">{t(section.titleKey)}</h4>
                  <ul className="space-y-4">
                    {section.links.map((link, linkIndex) => (
                      <li key={linkIndex}>
                        <a
                          href={link.href}
                          className="text-zinc-400 hover:text-white transition-colors duration-300 font-body text-sm"
                        >
                          {t(link.nameKey)}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>

            {/* Bottom section with improved styling */}
            <div className="mt-12 pt-8 border-t border-white/10">
              <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <p className="text-zinc-400 text-sm font-body">
                  &copy; {currentYear} <span className="text-jade-purple font-semibold">ChhlatBot</span> {t('footer_rights')}
                </p>
                <div className="flex flex-wrap justify-center gap-6 text-sm font-body">
                  <a
                    href="/privacy"
                    className="text-zinc-400 hover:text-white transition-colors duration-300 hover:underline underline-offset-4"
                  >
                    {t('footer_privacy')}
                  </a>
                  <a
                    href="/terms"
                    className="text-zinc-400 hover:text-white transition-colors duration-300 hover:underline underline-offset-4"
                  >
                    {t('footer_terms')}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}