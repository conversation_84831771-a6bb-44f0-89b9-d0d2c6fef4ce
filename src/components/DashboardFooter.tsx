'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { FaFacebook, FaInstagram, FaTiktok, FaTelegram, FaYoutube } from 'react-icons/fa'
import { useLanguage } from '@/context/LanguageContext'
import { useThemeConfig, useTheme } from '@/context/ThemeContext'

export default function DashboardFooter() {
  const currentYear = new Date().getFullYear()
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  const footerLinks = [
    {
      titleKey: 'footer_product',
      links: [
        { nameKey: 'footer_features', href: '/#features' },
        { nameKey: 'footer_pricing', href: '/#pricing' },
        { nameKey: 'footer_how_it_works', href: '/#how-it-works' },
        { nameKey: 'footer_faq', href: '/#faq' },
      ]
    },
    {
      titleKey: 'footer_company',
      links: [
        { nameKey: 'footer_blog', href: 'learn.chhlatbot/blog' },
        { nameKey: 'footer_docs', href: 'learn.chhlatbot/docs' },
      ]
    },
  ]

  const socialLinks = [
    { 
      name: 'Facebook', 
      icon: <FaFacebook className="text-lg" />, 
      href: 'https://www.facebook.com/chhlatbot'
    },
    { 
      name: 'TikTok', 
      icon: <FaTiktok className="text-lg" />, 
      href: 'https://www.tiktok.com/@chhlatbot'
    },
    { 
      name: 'Instagram', 
      icon: <FaInstagram className="text-lg" />, 
      href: 'https://www.instagram.com/chhlatbot'
    },
    { 
      name: 'YouTube', 
      icon: <FaYoutube className="text-lg" />, 
      href: 'https://www.youtube.com/@chhlatbot'
    },
    { 
      name: 'Telegram', 
      icon: <FaTelegram className="text-lg" />, 
      href: 'https://t.me/chhlatbot'
    },
  ]

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <footer className="text-white relative overflow-hidden mt-16">
      {/* Modern clean footer */}
      <div className="container mx-auto px-4 py-12 relative z-10">
        <div 
          className={`relative ${themeConfig.card} backdrop-blur-xl rounded-2xl p-8 border ${themeConfig.border} transition-all duration-300 overflow-hidden`}
          style={theme === 'dark' ? {
            boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
          } : {
            boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
          }}
        >
          {/* Simple background overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-dashboard-primary/3 to-transparent rounded-2xl"></div>

          {/* Content */}
          <div className="relative z-10">
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
              <div className="col-span-2">
                <div
                  className="mb-6 cursor-pointer group"
                  onClick={scrollToTop}
                >
                  <Image
                    src={themeConfig.logo}
                    alt="Chhlat Bot"
                    width={120}
                    height={40}
                    className="h-10 w-auto transition-transform duration-300 group-hover:scale-105"
                  />
                </div>
                <p className={`font-body mb-8 ${themeConfig.textSecondary} text-sm sm:text-base leading-relaxed`}>
                  {t('footer_description')}
                </p>
                <div className="flex space-x-3">
                  {socialLinks.map((social, index) => (
                    <motion.a
                      key={index}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`w-10 h-10 border-2 ${themeConfig.border} rounded-xl flex items-center justify-center ${themeConfig.textSecondary} ${themeConfig.borderHover} transition-all duration-300`}
                      whileHover={{ y: -1 }}
                      aria-label={social.name}
                    >
                      {social.icon}
                    </motion.a>
                  ))}
                </div>
              </div>

              {footerLinks.map((section, index) => (
                <div key={index}>
                  <h4 className={`text-lg font-semibold mb-6 font-title ${themeConfig.text}`}>{t(section.titleKey)}</h4>
                  <ul className="space-y-4">
                    {section.links.map((link, linkIndex) => (
                      <li key={linkIndex}>
                        <a
                          href={link.href}
                          className={`${themeConfig.textMuted} ${theme === 'dark' ? themeConfig.secondCardHover : themeConfig.hover} transition-all duration-300 font-body text-sm inline-block px-2 py-1 rounded`}
                        >
                          {t(link.nameKey)}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>

            {/* Bottom section with improved styling */}
            <div className={`mt-12 pt-8 border-t ${themeConfig.divider}`}>
              <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <p className={`${themeConfig.textMuted} text-sm font-body`}>
                  &copy; {currentYear} <span className="text-jade-purple font-semibold">ChhlatBot</span> {t('footer_rights')}
                </p>
                <div className="flex flex-wrap justify-center gap-6 text-sm font-body">
                  <a
                    href="/privacy"
                    className={`${themeConfig.textMuted} transition-all duration-300 hover:underline underline-offset-4 px-2 py-1 rounded`}
                  >
                    {t('footer_privacy')}
                  </a>
                  <a
                    href="/terms"
                    className={`${themeConfig.textMuted} transition-all duration-300 hover:underline underline-offset-4 px-2 py-1 rounded`}
                  >
                    {t('footer_terms')}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}