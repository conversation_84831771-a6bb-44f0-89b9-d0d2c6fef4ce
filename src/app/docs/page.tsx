'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { useState } from 'react'
import Footer from '@/components/Footer'

export default function DocsPage() {
  const [activeSection, setActiveSection] = useState('getting-started')
  const [mobileNavOpen, setMobileNavOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  const navigationSections = [
    {
      title: 'Getting Started',
      id: 'getting-started',
      items: [
        { title: 'Quick Start', id: 'quick-start' },
        { title: 'Account Setup', id: 'account-setup' },
        { title: 'First Steps', id: 'first-steps' }
      ]
    },
    {
      title: 'Platform Setup',
      id: 'platform-setup',
      items: [
        { title: 'Facebook Messenger', id: 'facebook-messenger' },
        { title: 'Instagram DM', id: 'instagram-dm' },
        { title: 'Telegram Bot', id: 'telegram-bot' }
      ]
    },
    {
      title: 'Training Your AI',
      id: 'training-ai',
      items: [
        { title: 'Chhlat Brain', id: 'chhlat-brain' },
        { title: 'Chhlat Tag', id: 'chhlat-tag' },
        { title: 'Voice Messages', id: 'voice-messages' },
        { title: 'Welcome Messages', id: 'welcome-messages' }
      ]
    },
    {
      title: 'Features',
      id: 'features',
      items: [
        { title: 'Multi-Language', id: 'multi-language' },
        { title: 'Response Types', id: 'response-types' },
        { title: 'Analytics', id: 'analytics' }
      ]
    },
    {
      title: 'Advanced',
      id: 'advanced',
      items: [
        { title: 'API Integration', id: 'api-integration' },
        { title: 'Webhooks', id: 'webhooks' }
      ]
    },
    {
      title: 'Troubleshooting',
      id: 'troubleshooting',
      items: [
        { title: 'Common Issues', id: 'common-issues' },
        { title: 'FAQ', id: 'faq' }
      ]
    }
  ]

  const scrollToSection = (sectionId: string) => {
    setActiveSection(sectionId)
    setMobileNavOpen(false) // Close mobile nav when selecting a section
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const filteredSections = navigationSections.filter(section =>
    section.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    section.items.some(item => 
      item.title.toLowerCase().includes(searchQuery.toLowerCase())
    )
  )

  return (
    <div className="min-h-screen bg-deep-blue">
      {/* Header */}
      <div className="border-b border-white/10 bg-deep-blue/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center">
            <img
              src="/images/white_tran_logo.svg"
              alt="ChhlatBot"
              className="h-8 w-auto"
            />
          </Link>
          <div className="flex items-center gap-4">
            <div className="hidden md:block text-white font-semibold">Documentation</div>
            
            {/* Mobile menu button */}
            <button
              onClick={() => setMobileNavOpen(!mobileNavOpen)}
              className="lg:hidden text-white p-2 hover:bg-white/10 rounded-lg transition-colors"
              aria-label="Toggle navigation"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {mobileNavOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Overlay */}
      {mobileNavOpen && (
        <div className="lg:hidden fixed inset-0 bg-black/50 z-40" onClick={() => setMobileNavOpen(false)} />
      )}

      <div className="flex">
        {/* Desktop Sidebar Navigation */}
        <div className="hidden lg:block w-64 bg-deep-blue/50 backdrop-blur-sm border-r border-white/10 min-h-screen sticky top-16 overflow-y-auto">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-white mb-4">Contents</h2>
            
            {/* Search */}
            <div className="mb-6">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search docs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full bg-white/5 border border-white/10 rounded-lg px-3 py-2 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-jade-purple focus:border-transparent"
                />
                <svg className="absolute right-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
            
            <nav className="space-y-6">
              {filteredSections.map((section) => (
                <div key={section.id}>
                  <button
                    onClick={() => scrollToSection(section.id)}
                    className={`text-left w-full font-medium mb-2 transition-colors ${
                      activeSection === section.id
                        ? 'text-jade-purple'
                        : 'text-gray-300 hover:text-white'
                    }`}
                  >
                    {section.title}
                  </button>
                  <ul className="space-y-2 ml-3 border-l border-white/10 pl-3">
                    {section.items.map((item) => (
                      <li key={item.id}>
                        <button
                          onClick={() => scrollToSection(item.id)}
                          className={`text-sm transition-colors block w-full text-left ${
                            activeSection === item.id
                              ? 'text-jade-purple'
                              : 'text-gray-400 hover:text-gray-300'
                          }`}
                        >
                          {item.title}
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </nav>
          </div>
        </div>

        {/* Mobile Navigation Sidebar */}
        <div className={`lg:hidden fixed inset-y-0 left-0 z-50 w-80 bg-deep-blue backdrop-blur-sm border-r border-white/10 transform transition-transform duration-300 ease-in-out ${
          mobileNavOpen ? 'translate-x-0' : '-translate-x-full'
        } overflow-y-auto`}>
          <div className="p-6 pt-20">
            <h2 className="text-lg font-semibold text-white mb-4">Contents</h2>
            
            {/* Mobile Search */}
            <div className="mb-6">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search docs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full bg-white/5 border border-white/10 rounded-lg px-3 py-2 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-jade-purple focus:border-transparent"
                />
                <svg className="absolute right-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
            
            <nav className="space-y-6">
              {filteredSections.map((section) => (
                <div key={section.id}>
                  <button
                    onClick={() => scrollToSection(section.id)}
                    className={`text-left w-full font-medium mb-2 transition-colors ${
                      activeSection === section.id
                        ? 'text-jade-purple'
                        : 'text-gray-300 hover:text-white'
                    }`}
                  >
                    {section.title}
                  </button>
                  <ul className="space-y-2 ml-3 border-l border-white/10 pl-3">
                    {section.items.map((item) => (
                      <li key={item.id}>
                        <button
                          onClick={() => scrollToSection(item.id)}
                          className={`text-sm transition-colors block w-full text-left ${
                            activeSection === item.id
                              ? 'text-jade-purple'
                              : 'text-gray-400 hover:text-gray-300'
                          }`}
                        >
                          {item.title}
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          <motion.div
            className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12 text-gray-300 max-w-4xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            {/* Getting Started Section */}
            <section id="getting-started" className="mb-12 sm:mb-16">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-4 text-white">ChhlatBot Documentation</h1>
              <p className="text-lg sm:text-xl text-gray-300 mb-6 sm:mb-8">Complete guide to setting up and using your AI-powered social media assistant.</p>

              <div id="quick-start" className="mb-8 sm:mb-12">
                <h2 className="text-xl sm:text-2xl font-semibold mb-4 text-white">Quick Start</h2>
                <p className="mb-4">Get your ChhlatBot up and running in under 15 minutes:</p>
                <div className="grid gap-4 sm:gap-6">
                  {[
                    {
                      step: 1,
                      title: "Create Account",
                      description: "Sign up and choose your plan (free trial included)",
                      icon: "👤"
                    },
                    {
                      step: 2,
                      title: "Connect Platforms",
                      description: "Link Facebook, Instagram, and Telegram accounts",
                      icon: "🔗"
                    },
                    {
                      step: 3,
                      title: "Train Your AI",
                      description: "Upload business information and customize responses",
                      icon: "🧠"
                    },
                    {
                      step: 4,
                      title: "Go Live",
                      description: "Activate your bot and start handling customers",
                      icon: "🚀"
                    }
                  ].map((item) => (
                    <motion.div
                      key={item.step}
                      className="bg-white/5 border border-white/10 rounded-lg p-4 sm:p-6 hover:bg-white/10 hover:border-jade-purple/30 transition-all duration-300 cursor-pointer group"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0">
                          <span className="bg-jade-purple text-white rounded-full w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center text-sm sm:text-base font-bold group-hover:bg-jade-purple-dark transition-colors">
                            {item.step}
                          </span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="text-xl sm:text-2xl">{item.icon}</span>
                            <h3 className="text-lg sm:text-xl font-semibold text-white group-hover:text-jade-purple-light transition-colors">
                              {item.title}
                            </h3>
                          </div>
                          <p className="text-gray-300 text-sm sm:text-base">{item.description}</p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              <div id="account-setup" className="mb-8 sm:mb-12">
                <h2 className="text-xl sm:text-2xl font-semibold mb-4 text-white">Account Setup</h2>
                <p className="mb-6 text-gray-300">During registration, you'll need to:</p>
                <div className="bg-white/5 border border-white/10 rounded-xl p-4 sm:p-6">
                  <ul className="space-y-3">
                    {[
                      { icon: "🌐", text: "Choose your primary language (Khmer or English recommended)" },
                      { icon: "🏢", text: "Select your business sector" },
                      { icon: "💳", text: "Pick a subscription plan" },
                      { icon: "✅", text: "Verify your account via Telegram" }
                    ].map((item, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <span className="text-lg mt-1 flex-shrink-0">{item.icon}</span>
                        <span className="text-gray-300">{item.text}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div id="first-steps" className="mb-8 sm:mb-12">
                <h2 className="text-xl sm:text-2xl font-semibold mb-4 text-white">First Steps</h2>
                <p className="mb-6 text-gray-300">Once your account is set up:</p>
                <div className="bg-gradient-to-r from-jade-purple/5 to-blue-500/5 border border-jade-purple/20 rounded-xl p-4 sm:p-6">
                  <ol className="space-y-4">
                    {[
                      { step: "1", text: "Access your dashboard", icon: "🎛️" },
                      { step: "2", text: "Connect at least one social media platform", icon: "🔗" },
                      { step: "3", text: "Add your first business question and response", icon: "❓" },
                      { step: "4", text: "Test your bot with a sample message", icon: "🧪" }
                    ].map((item, index) => (
                      <li key={index} className="flex items-center gap-4">
                        <span className="bg-jade-purple/20 text-jade-purple rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
                          {item.step}
                        </span>
                        <span className="text-lg mr-2 flex-shrink-0">{item.icon}</span>
                        <span className="text-gray-300">{item.text}</span>
                      </li>
                    ))}
                  </ol>
                </div>
              </div>
            </section>

            {/* Platform Setup Section */}
            <section id="platform-setup" className="mb-12 sm:mb-16">
              <div className="mb-6 sm:mb-8">
                <h1 className="text-2xl sm:text-3xl font-bold mb-4 text-white">Platform Setup</h1>
                <p className="text-gray-300 text-lg">Connect your social media platforms to start automating customer interactions.</p>
              </div>

              <div id="facebook-messenger" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-white">Facebook Messenger</h2>
                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-6 mb-4">
                  <h3 className="text-lg font-semibold mb-3 text-white">Requirements</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Facebook Business Page</li>
                    <li>Facebook Developer Account</li>
                    <li>Page Access Token</li>
                  </ul>
                </div>
                <h3 className="text-lg font-semibold mb-3 text-white">Setup Steps</h3>
                <ol className="list-decimal pl-6 space-y-3">
                  <li>Go to <a href="https://developers.facebook.com" className="text-jade-purple hover:underline">Facebook Developer Console</a></li>
                  <li>Create a new app and add Messenger product</li>
                  <li>Generate Page Access Token for your business page</li>
                  <li>Copy the webhook URL from your ChhlatBot dashboard</li>
                  <li>Configure webhook in Facebook Developer Console</li>
                  <li>Enter your Page Access Token in ChhlatBot</li>
                </ol>
              </div>

              <div id="instagram-dm" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-white">Instagram DM</h2>
                <div className="bg-pink-500/10 border border-pink-500/20 rounded-lg p-6 mb-4">
                  <h3 className="text-lg font-semibold mb-3 text-white">Requirements</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Instagram Business Account</li>
                    <li>Connected to Facebook Page</li>
                    <li>Instagram Page ID</li>
                  </ul>
                </div>
                <h3 className="text-lg font-semibold mb-3 text-white">Setup Steps</h3>
                <ol className="list-decimal pl-6 space-y-3">
                  <li>Convert Instagram account to Business Account</li>
                  <li>Connect Instagram to your Facebook Business Page</li>
                  <li>Use the same Facebook Page Access Token</li>
                  <li>Find your Instagram Page ID in Facebook Business Manager</li>
                  <li>Enter both Page Access Token and Instagram Page ID in ChhlatBot</li>
                </ol>
              </div>

              <div id="telegram-bot" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-white">Telegram Bot</h2>
                <div className="bg-cyan-500/10 border border-cyan-500/20 rounded-lg p-6 mb-4">
                  <h3 className="text-lg font-semibold mb-3 text-white">Requirements</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Telegram account</li>
                    <li>Bot Token from @BotFather</li>
                  </ul>
                </div>
                <h3 className="text-lg font-semibold mb-3 text-white">Setup Steps</h3>
                <ol className="list-decimal pl-6 space-y-3">
                  <li>Open Telegram and search for @BotFather</li>
                  <li>Send 
                    <div className="inline-flex items-center gap-2 bg-white/10 px-2 py-1 rounded-md ml-1">
                      <code className="text-jade-purple-light">/newbot</code>
                      <button
                        onClick={() => copyToClipboard('/newbot')}
                        className="text-gray-400 hover:text-white transition-colors p-1"
                        title="Copy command"
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </button>
                    </div> command</li>
                  <li>Follow instructions to create your bot</li>
                  <li>Copy the Bot Token provided by BotFather</li>
                  <li>Enter the Bot Token in your ChhlatBot dashboard</li>
                </ol>
              </div>
            </section>

            {/* Training AI Section */}
            <section id="training-ai" className="mb-12 sm:mb-16">
              <div className="mb-6 sm:mb-8">
                <h1 className="text-2xl sm:text-3xl font-bold mb-4 text-white">Training Your AI</h1>
                <p className="text-gray-300 text-lg">Teach your AI assistant with business knowledge and custom responses.</p>
              </div>

              <div id="chhlat-brain" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-white">Chhlat Brain (Knowledge Base)</h2>
                <p className="mb-4">Your AI's knowledge comes from the questions and answers you provide:</p>
                <div className="grid gap-4 mb-6">
                  <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                    <h3 className="font-semibold text-white mb-2">Text Responses</h3>
                    <p className="text-gray-300">Add common questions with text-based replies</p>
                  </div>
                  <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                    <h3 className="font-semibold text-white mb-2">Voice Messages</h3>
                    <p className="text-gray-300">Record audio responses using our Telegram bot</p>
                  </div>
                  <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-4">
                    <h3 className="font-semibold text-white mb-2">Image Attachments</h3>
                    <p className="text-gray-300">Link photos from your Chhlat Tag gallery</p>
                  </div>
                </div>
                <h3 className="text-lg font-semibold mb-3 text-white">Best Practices</h3>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Write questions as customers would ask them</li>
                  <li>Use your primary language consistently</li>
                  <li>Include variations of the same question</li>
                  <li>Keep responses helpful but concise</li>
                </ul>
              </div>

              <div id="chhlat-tag" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-white">Chhlat Tag (Photo Gallery)</h2>
                <p className="mb-4">Organize business images for automated responses:</p>
                <ul className="list-disc pl-6 space-y-2 mb-4">
                  <li>Upload product photos, menus, or service images</li>
                  <li>Assign unique IDs for easy reference</li>
                  <li>Link photos to questions in knowledge base</li>
                  <li>Maximum 5MB per image (JPG/PNG)</li>
                </ul>
              </div>
            </section>

            {/* Continue with other sections... */}
            <section id="troubleshooting" className="mb-16">
              <h1 className="text-3xl font-bold mb-8 text-white">Troubleshooting</h1>

              <div id="common-issues" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-white">Common Issues</h2>
                <div className="space-y-6">
                  <div className="bg-white/5 border border-white/10 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-white mb-2">Bot not responding to messages</h3>
                    <p className="text-gray-300 mb-3">Solutions:</p>
                    <ul className="list-disc pl-6 space-y-1 text-gray-300">
                      <li>Check platform connection status</li>
                      <li>Verify access tokens are valid</li>
                      <li>Ensure webhook settings are correct</li>
                      <li>Test with simple questions first</li>
                    </ul>
                  </div>
                  <div className="bg-white/5 border border-white/10 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-white mb-2">Wrong language responses</h3>
                    <p className="text-gray-300 mb-3">Solutions:</p>
                    <ul className="list-disc pl-6 space-y-1 text-gray-300">
                      <li>Check primary language settings</li>
                      <li>Ensure knowledge base is in correct language</li>
                      <li>Update language preferences in settings</li>
                    </ul>
                  </div>
                </div>
              </div>
            </section>

            {/* Support Section */}
            <section className="mb-12 sm:mb-16">
              <motion.div 
                className="bg-gradient-to-br from-jade-purple/10 to-jade-purple/5 border border-jade-purple/20 rounded-xl p-6 sm:p-8 text-center"
                initial={{ opacity: 0, scale: 0.95 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
              >
                <div className="mb-6">
                  <div className="w-16 h-16 mx-auto mb-4 bg-jade-purple/20 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-jade-purple" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                  <h2 className="text-xl sm:text-2xl font-semibold mb-4 text-white">Need Help?</h2>
                  <p className="text-gray-300 mb-6 max-w-2xl mx-auto">Our support team is ready to help you get the most out of ChhlatBot. Whether you need setup assistance or have questions about features, we're here for you.</p>
                </div>
                <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                  <Link 
                    href="/dashboard" 
                    className="bg-jade-purple text-white px-6 py-3 rounded-lg hover:bg-jade-purple-dark transition-all duration-200 font-medium hover:shadow-lg hover:shadow-jade-purple/25 transform hover:-translate-y-0.5"
                  >
                    Go to Dashboard
                  </Link>
                  <a 
                    target="_blank"
                    href="https://t.me/chhlatbot" 
                    className="border border-jade-purple text-jade-purple px-6 py-3 rounded-lg hover:bg-jade-purple/10 hover:border-jade-purple-light transition-all duration-200 font-medium"
                  >
                    Contact Support
                  </a>
                </div>
              </motion.div>
            </section>
          </motion.div>
        </div>
      </div>

      <Footer />
    </div>
  )
}