import { NextRequest, NextResponse } from 'next/server'
import { queryMany } from '@/lib/postgres'
import { getCategoryBySlug, getAuthor } from '@/lib/blog-constants'

interface BlogPost {
  id: number
  title: string
  slug: string
  excerpt: string
  featured_image: string | null
  author_key: string
  category_slug: string
  tags: string[]
  featured: boolean
  read_time_minutes: number
  view_count: number
  published_at: string
  total_count: string
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl
    const limit = parseInt(searchParams.get('limit') || '10')
    const page = parseInt(searchParams.get('page') || '1')
    const offset = (page - 1) * limit
    const category = searchParams.get('category') || null
    const search = searchParams.get('search') || null
    const featured = searchParams.get('featured') === 'true'

    const posts = await queryMany<BlogPost>(
      'SELECT * FROM public.get_blog_posts($1, $2, $3, $4, $5)',
      [limit, offset, category, search, featured]
    )

    const totalCount = posts.length > 0 ? parseInt(posts[0].total_count) : 0
    const totalPages = Math.ceil(totalCount / limit)

    return NextResponse.json({
      success: true,
      data: {
        posts: posts.map(post => {
          const category = getCategoryBySlug(post.category_slug)
          const author = getAuthor(post.author_key)
          
          return {
            id: post.id,
            title: post.title,
            slug: post.slug,
            excerpt: post.excerpt,
            featuredImage: post.featured_image,
            author: author.name,
            category: {
              name: category?.name || 'Unknown',
              slug: post.category_slug,
              color: category?.color || '#6135e6'
            },
            tags: post.tags || [],
            featured: post.featured,
            readTime: `${post.read_time_minutes} min read`,
            viewCount: post.view_count,
            publishedAt: post.published_at
          }
        }),
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        }
      }
    })
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch blog posts' },
      { status: 500 }
    )
  }
}