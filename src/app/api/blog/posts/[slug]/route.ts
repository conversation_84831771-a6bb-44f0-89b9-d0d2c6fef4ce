import { NextRequest, NextResponse } from 'next/server'
import { queryOne, queryMany } from '@/lib/postgres'
import { getCategoryBySlug, getAuthor } from '@/lib/blog-constants'

interface BlogPostDetail {
  id: number
  title: string
  slug: string
  excerpt: string
  content: string
  featured_image: string | null
  author_key: string
  category_slug: string
  tags: string[]
  featured: boolean
  meta_title: string | null
  meta_description: string | null
  read_time_minutes: number
  view_count: number
  published_at: string
}

interface RelatedPost {
  id: number
  title: string
  slug: string
  excerpt: string
  featured_image: string | null
  author_key: string
  category_slug: string
  read_time_minutes: number
  published_at: string
}

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params

    // Get the main blog post
    const post = await queryOne<BlogPostDetail>(
      'SELECT * FROM public.get_blog_post_by_slug($1)',
      [slug]
    )

    if (!post) {
      return NextResponse.json(
        { success: false, error: 'Blog post not found' },
        { status: 404 }
      )
    }

    // Get related posts
    const relatedPosts = await queryMany<RelatedPost>(
      'SELECT * FROM public.get_related_posts($1, $2)',
      [post.id, 3]
    )

    const category = getCategoryBySlug(post.category_slug)
    const author = getAuthor(post.author_key)

    return NextResponse.json({
      success: true,
      data: {
        post: {
          id: post.id,
          title: post.title,
          slug: post.slug,
          excerpt: post.excerpt,
          content: post.content,
          featuredImage: post.featured_image,
          author: {
            name: author.name,
            email: author.email
          },
          category: {
            name: category?.name || 'Unknown',
            slug: post.category_slug,
            color: category?.color || '#6135e6'
          },
          tags: post.tags || [],
          featured: post.featured,
          meta: {
            title: post.meta_title || post.title,
            description: post.meta_description || post.excerpt
          },
          readTime: `${post.read_time_minutes} min read`,
          viewCount: post.view_count,
          publishedAt: post.published_at
        },
        relatedPosts: relatedPosts.map(related => {
          const relatedCategory = getCategoryBySlug(related.category_slug)
          const relatedAuthor = getAuthor(related.author_key)
          
          return {
            id: related.id,
            title: related.title,
            slug: related.slug,
            excerpt: related.excerpt,
            featuredImage: related.featured_image,
            author: relatedAuthor.name,
            category: {
              name: relatedCategory?.name || 'Unknown',
              slug: related.category_slug,
              color: relatedCategory?.color || '#6135e6'
            },
            readTime: `${related.read_time_minutes} min read`,
            publishedAt: related.published_at
          }
        })
      }
    })
  } catch (error) {
    console.error('Error fetching blog post:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch blog post' },
      { status: 500 }
    )
  }
}