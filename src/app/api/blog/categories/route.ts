import { NextResponse } from 'next/server'
import { queryMany } from '@/lib/postgres'
import { BLOG_CATEGORIES } from '@/lib/blog-constants'

interface CategoryCount {
  category_slug: string
  post_count: number
}

export async function GET() {
  try {
    // Get post counts per category from database
    const categoryCounts = await queryMany<CategoryCount>(`
      SELECT 
        category_slug,
        COUNT(*) as post_count
      FROM public.blog_posts 
      WHERE status = 'published'
      GROUP BY category_slug
    `)

    const countMap = new Map(
      categoryCounts.map(c => [c.category_slug, parseInt(c.post_count.toString())])
    )

    // Combine static categories with dynamic post counts
    const categories = Object.values(BLOG_CATEGORIES).map(cat => ({
      name: cat.name,
      slug: cat.slug,
      description: cat.description,
      color: cat.color,
      postCount: countMap.get(cat.slug) || 0
    }))

    return NextResponse.json({
      success: true,
      data: categories
    })
  } catch (error) {
    console.error('Error fetching blog categories:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch categories' },
      { status: 500 }
    )
  }
}