import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { queryOne } from '@/lib/postgres'
import { serverCache } from '@/lib/cache'

// Pre-built connection limits by plan type
const PLAN_CONNECTION_LIMITS: Record<string, number> = {
  Intern: 1,
  Assistant: 3,
  Manager: 4,
  free: 1 // Default fallback
}

// Define the sanitized credentials type (what we send to client)
type SanitizedCredentials = {
  id: number
  client_id: string
  fb_name?: string
  ig_name?: string
  tg_name?: string
  wa_name?: string
  web_name?: string
  web_domain?: string
  fb_status?: number
  ig_status?: number
  wa_status?: number
  tg_status?: number
  web_status?: number
  tg_connection_type?: 'Bot' | 'Business'
  // Note: We don't expose URLs, tokens, tg_id_name, or sensitive identifiers for security
}

type ConnectionDataResponse = {
  limit: number
  plan_type: string
  credentials: SanitizedCredentials
}

export async function GET() {
  try {
    // Single authentication check
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false, 
        body: null, 
        error_msg: 'Unauthorized' 
      }, { status: 401 })
    }

    // Validate client ID
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Check dashboard cache for plan_type first
    const dashboardCacheKey = `dashboard_${authId}`
    const cachedDashboardData = serverCache.get(dashboardCacheKey)
    let planType = 'free' // Default fallback

    if (cachedDashboardData?.clientInfo?.plan_type) {
      planType = cachedDashboardData.clientInfo.plan_type
    } else {
      // Fallback: query database for plan_type only
      const clientResult = await queryOne('SELECT plan_type FROM clients WHERE client_id = $1', [clientId])
      planType = clientResult?.plan_type || 'free'
    }

    const limit = PLAN_CONNECTION_LIMITS[planType] || PLAN_CONNECTION_LIMITS.free

    // Get connection credentials separately
    const credentialsResult = await queryOne(`
      SELECT id, client_id, fb_name, ig_name, tg_name, wa_name, 
             web_name, web_domain, fb_status, ig_status, wa_status, 
             tg_status, web_status, tg_biz_id
      FROM client_credentials 
      WHERE client_id = $1
    `, [clientId])

    // If no credentials record exists, return error - credentials should be created by backend
    if (!credentialsResult) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'No connection credentials found. Please contact support.'
      }, { status: 404 })
    }

    // Determine Telegram connection type based on tg_biz_id presence
    const tg_connection_type = credentialsResult.tg_biz_id ? 'Business' : 'Bot'

    // Return sanitized credentials data
    const sanitizedCredentials: SanitizedCredentials = {
      id: credentialsResult.id,
      client_id: credentialsResult.client_id,
      fb_name: credentialsResult.fb_name,
      ig_name: credentialsResult.ig_name,
      tg_name: credentialsResult.tg_name,
      wa_name: credentialsResult.wa_name,
      web_name: credentialsResult.web_name,
      web_domain: credentialsResult.web_domain,
      fb_status: credentialsResult.fb_status,
      ig_status: credentialsResult.ig_status,
      wa_status: credentialsResult.wa_status,
      tg_status: credentialsResult.tg_status,
      web_status: credentialsResult.web_status,
      tg_connection_type: tg_connection_type
    }

    const response: ConnectionDataResponse = {
      limit,
      plan_type: planType,
      credentials: sanitizedCredentials
    }

    return NextResponse.json({
      success: true,
      body: response,
      error_msg: null
    })

  } catch (error) {
    console.error('Error in connection-data API:', error)
    return NextResponse.json({
      success: false,
      body: null,
      error_msg: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 })
  }
}
