'use client'

import Link from 'next/link'
import Image from 'next/image'
import { useState, useEffect, useRef } from 'react'
import { useAuth } from '@/context/AuthContext'
import DashboardFooter from '@/components/DashboardFooter'
import ConnectPageSkeleton from '@/components/ConnectPageSkeleton'
import { FaPaperPlane, FaFacebookMessenger, FaInstagram, FaGlobe, FaChevronDown } from 'react-icons/fa'
import { FaTiktok } from 'react-icons/fa6'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { Button, LinkButton, DashboardHeader, PlatformCard, PlatformConnectionForm } from '@/components/ui'
import { UpdateStatusOverlay } from '@/components/ui/knowledge'
import { UpdateConfirmationModal, CancelConfirmationModal } from '@/components/ui/modals'

type WebhookState = {
  [key: string]: {
    isCopied: boolean;
  };
};

// Define platform type
type Platform = 'facebook' | 'instagram' | 'whatsapp' | 'telegram' | 'web' | 'tiktok';

// Define sanitized client credentials type (from API)
type ClientCredentials = {
  id: number;
  client_id: string;
  fb_name?: string;
  ig_name?: string;
  tg_name?: string;
  wa_name?: string;
  web_name?: string;
  web_domain?: string;
  fb_status?: number;
  ig_status?: number;
  wa_status?: number;
  tg_status?: number;
  web_status?: number;
  tg_connection_type?: 'Bot' | 'Business';
  // Security: URLs, tokens, and sensitive identifiers are not exposed
};

export default function ConnectAccountsPage() {
  const { user: _ } = useAuth() // Unused but kept for context
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // State variables
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  // Overlay state for UpdateStatusOverlay
  const [overlayStatus, setOverlayStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [overlayMessage, setOverlayMessage] = useState<string>('')
  const [isClient, setIsClient] = useState(false)
  const [credentials, setCredentials] = useState<ClientCredentials | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [connectingPlatform, setConnectingPlatform] = useState<string | null>(null)
  const [isConnecting, setIsConnecting] = useState(false) // For confirmation popup spinning
  const [selectedPlatform, setSelectedPlatform] = useState<Platform>('telegram')
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [credentialsLoaded, setCredentialsLoaded] = useState<boolean>(false)

  const [availablePlatforms, setAvailablePlatforms] = useState<Platform[]>([])
  const [webhookStates, setWebhookStates] = useState<WebhookState>({
    'fb-webhook': { isCopied: false },
    'ig-webhook': { isCopied: false },
    'wa-webhook': { isCopied: false },
    'web-webhook': { isCopied: false },
    'privacy-policy': { isCopied: false },
    'telegram-bot-name': { isCopied: false }
  })
  // Separate states for different modal types
  const [showConnectConfirmation, setShowConnectConfirmation] = useState<{show: boolean, platform: string | null}>({
    show: false,
    platform: null
  })
  const [connectConfirmationLoading, setConnectConfirmationLoading] = useState(false)
  const [showEnableConfirmation, setShowEnableConfirmation] = useState<{show: boolean, platform: string | null}>({
    show: false,
    platform: null
  })
  const [enableConfirmationLoading, setEnableConfirmationLoading] = useState(false)
  const [showDisableConfirmation, setShowDisableConfirmation] = useState<{show: boolean, platform: string | null}>({
    show: false,
    platform: null
  })
  const [disableConfirmationLoading, setDisableConfirmationLoading] = useState(false)

  // Track connected platforms
  const [connectedPlatforms, setConnectedPlatforms] = useState<{[key: string]: boolean}>({
    facebook: false,
    instagram: false,
    whatsapp: false,
    telegram: false,
    web: false
  })

  // Track platform status (enabled/disabled)
  const [platformStatus, setPlatformStatus] = useState<{[key: string]: boolean}>({
    facebook: false,
    instagram: false,
    whatsapp: false,
    telegram: false,
    web: false
  })

  // Store token values (used to track token changes)
  const [tokenValues, setTokenValues] = useState<{[key: string]: string}>({
    facebook: '',
    instagram: '',
    whatsapp: '',
    telegram: ''
  })



  // We're using password input type to mask tokens, no need for additional state

  // Refs for input fields
  const facebookTokenRef = useRef<HTMLInputElement>(null)
  const instagramTokenRef = useRef<HTMLInputElement>(null)
  const instagramIdRef = useRef<HTMLInputElement>(null)
  const telegramTokenRef = useRef<HTMLInputElement>(null)
  const telegramBusinessUsernameRef = useRef<HTMLInputElement>(null)
  const webDomainRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // State for Telegram connection type (bot or business)
  const [telegramConnectionType, setTelegramConnectionType] = useState<'bot' | 'business'>('business')

  // Platform data with icons and names
  const platforms = [
    { id: 'telegram', name: t('telegram'), icon: <FaPaperPlane size={16} className="text-blue-400" /> },
    { id: 'facebook', name: t('facebook_messenger'), icon: <FaFacebookMessenger size={18} className="text-blue-500" /> },
    { id: 'instagram', name: t('instagram'), icon: <FaInstagram size={18} className="text-pink-500" /> },
    // { id: 'whatsapp', name: 'WhatsApp', icon: <FaWhatsapp size={18} className="text-green-500" /> },
    { id: 'web', name: t('web_api_coming_soon_short'), icon: <FaGlobe size={18} className="text-purple-500" />, disabled: true },
    { id: 'tiktok', name: t('tiktok_coming_soon_short'), icon: <FaTiktok size={18} className={themeConfig.text} />, disabled: true }
  ]

  // Handle platform selection
  const handleSelectPlatform = (platform: Platform) => {
    // First close the dropdown immediately for better responsiveness
    setIsDropdownOpen(false)
    // Then update the selected platform (this will trigger a re-render)
    // No need for setTimeout, we'll handle this with proper animations
    setSelectedPlatform(platform)
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Disable page scroll when any popup is open
  useEffect(() => {
    if (errorMessage || successMessage || overlayStatus !== 'idle' || isLoading || 
        showConnectConfirmation.show || showEnableConfirmation.show || showDisableConfirmation.show) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [errorMessage, successMessage, overlayStatus, isLoading, showConnectConfirmation.show, showEnableConfirmation.show, showDisableConfirmation.show])

  // Set isClient to true when component mounts and fetch credentials
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Fetch connection data when component mounts
  useEffect(() => {
    fetchConnectionData();
  }, [])

  // State for connection limit
  const [connectionLimit, setConnectionLimit] = useState<number>(1);
  const [connectionLimitLoaded, setConnectionLimitLoaded] = useState<boolean>(false);

  // Get connection limit
  const getConnectionLimit = () => {
    return connectionLimit;
  };

  // Fetch connection data (credentials + limit) via consolidated API (Security Enhanced)
  const fetchConnectionData = async () => {
    try {
      setIsLoading(true)

      // Call consolidated connection-data API
      const response = await fetch('/api/connection-data')
      const responseData = await response.json()

      if (!response.ok) {
        console.error('Error fetching connection data:', responseData.error_msg)
        showError(`${t('failed_load_connection_data')}: ${responseData.error_msg}`)
        return
      }

      if (!responseData.success) {
        console.error('Error fetching connection data:', responseData.error_msg)
        showError(`${t('failed_load_connection_data')}: ${responseData.error_msg}`)
        return
      }

      // Set connection limit
      const limit = responseData.body.limit || 1;
      setConnectionLimit(limit);
      setConnectionLimitLoaded(true);

      // Set credentials data
      const data = responseData.body.credentials
      setCredentials(data)

      // Update token values (using has_* flags from API)
      const newTokenValues = {
        facebook: '',
        instagram: '',
        whatsapp: '',
        telegram: ''
      }
      setTokenValues(newTokenValues)

      // Set connected platforms based on platform name fields from API
      const newConnectedPlatforms = {
        facebook: !!(data.fb_name),
        instagram: !!(data.ig_name),
        whatsapp: !!(data.wa_name),
        telegram: !!(data.tg_name), // Use tg_name for both bot and business connections
        web: !!(data.web_name)
      }
      setConnectedPlatforms(newConnectedPlatforms)

      // Set platform status based on status fields (default to 1/true if status field is not present)
      // For Telegram, use tg_status for both bot and business connections
      const telegramConnected = !!(data.tg_name);
      const telegramStatus = telegramConnected ? (data.tg_status === undefined ? true : data.tg_status === 1) : false;

      const newPlatformStatus = {
        facebook: data.fb_status === undefined ? true : data.fb_status === 1,
        instagram: data.ig_status === undefined ? true : data.ig_status === 1,
        whatsapp: data.wa_status === undefined ? true : data.wa_status === 1,
        telegram: telegramStatus,
        web: data.web_status === undefined ? true : data.web_status === 1
      }
      setPlatformStatus(newPlatformStatus)

      // Determine which platforms are already connected
      const connectedPlatformsList: Platform[] = [];
      if (data.fb_name) connectedPlatformsList.push('facebook');
      if (data.ig_name) connectedPlatformsList.push('instagram');
      // if (data.wa_name) connectedPlatformsList.push('whatsapp');
      // Check for telegram name (covers both bot and business connections)
      if (data.tg_name) connectedPlatformsList.push('telegram');

      // Consider web as connected if there's a web_name
      if (data.web_name) connectedPlatformsList.push('web');

      // Set available platforms (all platforms except those already connected or disabled)
      const allPlatforms: Platform[] = ['telegram', 'facebook', 'instagram', /* 'whatsapp', */ 'web', 'tiktok'];
      const available = allPlatforms.filter(p =>
        !connectedPlatformsList.includes(p) || p === 'tiktok'
      );
      setAvailablePlatforms(available);

      // Set the default selected platform to the first available one
      if (available.length > 0 && available[0] !== 'tiktok') {
        setSelectedPlatform(available[0]);
      }

      // Note: We no longer populate input fields with token values for security
    } catch (error) {
      console.error('Unexpected error in fetchConnectionData:', error)
      showError(t('unexpected_error'))
    } finally {
      setIsLoading(false)
      setCredentialsLoaded(true)
    }
  }

  // Helper function to check if all data is loaded
  const isDataReady = () => {
    return credentialsLoaded && connectionLimitLoaded;
  }

  // Helper functions for overlay management
  const showError = (message: string) => {
    setOverlayStatus('error')
    setOverlayMessage(message)
  }

  const showSuccess = (message: string) => {
    setOverlayStatus('success')
    setOverlayMessage(message)
    
    // Auto-hide success overlay after 1.5 seconds
    setTimeout(() => {
      hideOverlay()
    }, 1500)
  }

  const hideOverlay = () => {
    setOverlayStatus('idle')
    setOverlayMessage('')
  }



  const handleCopyWebhook = async (id: string) => {
    try {
      let url = '';

      // For privacy policy, use direct URL
      if (id === 'privacy-policy') {
        url = 'https://www.chhlatbot.com/privacy';
      } else {
        // For webhook URLs, fetch from secure API
        const response = await fetch('/api/webhook-url', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ webhookId: id }),
        });

        if (!response.ok) {
          console.error('Failed to fetch webhook URL');
          showError(t('failed_to_copy'));
          return;
        }

        const data = await response.json();
        if (!data.success) {
          console.error('Failed to fetch webhook URL:', data.error_msg);
          showError(t('failed_to_copy'));
          return;
        }
        url = data.body.url;
      }

      // Try using the newer navigator.clipboard API first
      try {
        await navigator.clipboard.writeText(url);
      } catch (clipboardError) {
        // Fallback for older browsers or when Clipboard API fails
        const textArea = document.createElement('textarea');
        textArea.value = url;
        textArea.style.position = 'fixed';
        textArea.style.left = '-9999px';
        textArea.style.top = '0';
        document.body.appendChild(textArea);
        
        try {
          textArea.select();
          textArea.setSelectionRange(0, 99999); // For mobile devices
          const successful = document.execCommand('copy');
          if (!successful) throw new Error('Copy command failed');
        } finally {
          document.body.removeChild(textArea);
        }
      }

      // Show success state
      setWebhookStates(prev => ({
        ...prev,
        [id]: { isCopied: true }
      }));

      // Show success message
      // setSuccessMessage(null);

      // Reset copy status and clear success message after 3 seconds
      setTimeout(() => {
        setWebhookStates(prev => ({
          ...prev,
          [id]: { isCopied: false }
        }));
        // setSuccessMessage(null);
      }, 3000);
    } catch (err) {
      console.error('Failed to copy:', err);
      showError(t('failed_to_copy'));
      
      // Clear error message after 3 seconds
      setTimeout(() => {
        hideOverlay();
      }, 3000);
    }
  };

  // Handle copying bot name for Telegram Business
  const handleCopyBotName = async (id: string = 'telegram-bot-name') => {
    try {
      const botName = 'yourchhlatbot';
      await navigator.clipboard.writeText(botName);

      // Show success state using existing webhook states
      setWebhookStates(prev => ({
        ...prev,
        [id]: { isCopied: true }
      }));

      // Reset copy status after 2 seconds
      setTimeout(() => {
        setWebhookStates(prev => ({
          ...prev,
          [id]: { isCopied: false }
        }));
      }, 2000);
    } catch (err) {
      console.error('Failed to copy bot name:', err);
    }
  };

  // We've removed the toggleWebhookVisibility function as it's no longer needed

  // Handle secure token input changes
  const handleTokenChange = (platform: string, value: string) => {
    // Store the actual token value in the ref
    switch (platform) {
      case 'facebook':
        if (facebookTokenRef.current) facebookTokenRef.current.value = value;
        break;
      case 'instagram':
        if (instagramTokenRef.current) instagramTokenRef.current.value = value;
        break;
      /* case 'whatsapp':
        if (whatsappTokenRef.current) whatsappTokenRef.current.value = value;
        break; */
      case 'telegram':
        if (telegramTokenRef.current) telegramTokenRef.current.value = value;
        break;
    }
  }

  // Initiate toggle platform status - show appropriate confirmation popup
  const initiateTogglePlatformStatus = (platform: string) => {
    // Get current status
    const currentStatus = platformStatus[platform];
    const newStatus = !currentStatus;

    // Show appropriate confirmation dialog
    if (newStatus) {
      // Enabling platform - use UpdateConfirmationModal
      setShowEnableConfirmation({
        show: true,
        platform
      });
    } else {
      // Disabling platform - use CancelConfirmationModal
      setShowDisableConfirmation({
        show: true,
        platform
      });
    }
  };

  // Handle toggling platform status after confirmation - simplified with new API
  const handleTogglePlatformStatus = async (platform: string) => {
    try {
      // Get current status to determine which loading state to set
      const currentStatus = platformStatus[platform];
      const newStatus = !currentStatus;
      
      // Set appropriate loading state
      if (newStatus) {
        setEnableConfirmationLoading(true);
      } else {
        setDisableConfirmationLoading(true);
      }
      
      // Set connecting state but don't show loading overlay
      setIsConnecting(true);

      // Call the new centralized toggle API
      const response = await fetch('/api/platform/toggle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: platform,
          status: newStatus
        }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        // Clear modal states and show error overlay
        setShowEnableConfirmation({ show: false, platform: null });
        setShowDisableConfirmation({ show: false, platform: null });
        setEnableConfirmationLoading(false);
        setDisableConfirmationLoading(false);
        // Handle specific error messages from backend
        showError(responseData.error_msg || `Failed to update ${platform} status`);
        // Clear error message after 3 seconds
        setTimeout(() => {
          hideOverlay();
        }, 3000);
        return;
      }

      if (!responseData.success) {
        // Clear modal states and show error overlay
        setShowEnableConfirmation({ show: false, platform: null });
        setShowDisableConfirmation({ show: false, platform: null });
        setEnableConfirmationLoading(false);
        setDisableConfirmationLoading(false);
        showError(responseData.error_msg || `Unexpected response when updating ${platform} status`);
        // Clear error message after 3 seconds
        setTimeout(() => {
          hideOverlay();
        }, 3000);
        return;
      }

      // API call successful - clear modal states and update local state

      // Clear modal states and loading
      setShowEnableConfirmation({ show: false, platform: null });
      setShowDisableConfirmation({ show: false, platform: null });
      setEnableConfirmationLoading(false);
      setDisableConfirmationLoading(false);

      // Update local state
      setPlatformStatus(prev => ({
        ...prev,
        [platform]: newStatus
      }));

      // Show success message (showSuccess already handles auto-dismiss after 3 seconds)
      const status = newStatus ? t('enable').toLowerCase() : t('disable').toLowerCase();
      showSuccess(t('platform_status_changed').replace('{platform}', platform).replace('{status}', status));

    } catch (error) {
      console.error(`Error toggling ${platform} status:`, error);
      // Clear modal states and show error overlay
      setShowEnableConfirmation({ show: false, platform: null });
      setShowDisableConfirmation({ show: false, platform: null });
      setEnableConfirmationLoading(false);
      setDisableConfirmationLoading(false);
      showError(`Failed to update ${platform} status`);
      // Clear error message after 3 seconds
      setTimeout(() => {
        hideOverlay();
      }, 3000);
    } finally {
      setIsConnecting(false);
      setEnableConfirmationLoading(false);
      setDisableConfirmationLoading(false);
    }
  };

  // Show confirmation dialog before connecting
  const initiateConnect = (platform: string) => {
    // Don't proceed if data is not ready yet
    if (!isDataReady()) {
      showError(t('loading'));
      setTimeout(() => {
        hideOverlay();
      }, 2000);
      return;
    }

    // Check if we've reached the connection limit
    const connectedCount = Object.values(connectedPlatforms).filter(Boolean).length;

    // Check if this platform is already connected
    const isAlreadyConnected = connectedPlatforms[platform];

    const connectionLimit = getConnectionLimit();
    if (connectedCount >= connectionLimit && !isAlreadyConnected) {
      showError(t('connection_limit_reached_error').replace('{limit}', connectionLimit.toString()));

      // Auto-dismiss error message after 3 seconds
      setTimeout(() => {
        hideOverlay();
      }, 3000);

      return;
    }

    // Get token or domain from input ref
    let value = '';
    let instagramId = '';

    switch (platform) {
      case 'facebook':
        value = facebookTokenRef.current?.value || '';
        break;
      case 'instagram':
        value = instagramTokenRef.current?.value || '';
        instagramId = instagramIdRef.current?.value || '';
        break;
      /* case 'whatsapp':
        value = whatsappTokenRef.current?.value || '';
        whatsappId = whatsappIdRef.current?.value || '';
        break; */
      case 'telegram':
        if (telegramConnectionType === 'bot') {
          value = telegramTokenRef.current?.value || '';
        } else {
          // For Telegram Business, get username instead of token
          value = telegramBusinessUsernameRef.current?.value || '';
        }
        break;
      case 'web':
        value = webDomainRef.current?.value || '';
        break;
    }

    if (!value || value.trim() === '') {
      let errorKey = 'enter_valid_token';
      if (platform === 'web') {
        errorKey = 'enter_valid_domain';
      } else if (platform === 'telegram' && telegramConnectionType === 'business') {
        errorKey = 'enter_valid_telegram_username';
      }
      showError(t(errorKey).replace('{platform}', platform));

      // Auto-dismiss error message after 3 seconds
      setTimeout(() => {
        hideOverlay();
      }, 1000);

      return;
    }

    // Check for Instagram ID
    if (platform === 'instagram' && (!instagramId || instagramId.trim() === '')) {
      showError(t('enter_valid_page_id'));

      // Auto-dismiss error message after 3 seconds
      setTimeout(() => {
        hideOverlay();
      }, 1000);

      return;
    }

    // Check for WhatsApp ID
    /* if (platform === 'whatsapp' && (!whatsappId || whatsappId.trim() === '')) {
      setErrorMessage('Please enter a valid ID for WhatsApp.');

      // Auto-dismiss error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 1000);

      return;
    } */

    // Show connect confirmation dialog
    setShowConnectConfirmation({
      show: true,
      platform
    });
  };

  // Handle platform connection after confirmation
  const handleConnect = async (platform: string) => {
    try {
      // Set loading state for the confirmation modal
      setConnectConfirmationLoading(true);
      
      // Set connecting state but don't show overlay yet
      setIsConnecting(true);


      // Get token or domain from input ref
      let token = '';
      let domain = '';
      let webhookUrl = '';
      const isWebPlatform = platform === 'web';

      switch (platform) {
        case 'facebook':
          token = facebookTokenRef.current?.value || '';
          webhookUrl = ''; // Backend will fetch the actual webhook URL
          break;
        case 'instagram':
          token = instagramTokenRef.current?.value || '';
          webhookUrl = ''; // Backend will fetch the actual webhook URL
          break;
        /* case 'whatsapp':
          token = whatsappTokenRef.current?.value || '';
          webhookUrl = ''; // Backend will fetch the actual webhook URL
          break; */
        case 'telegram':
          if (telegramConnectionType === 'bot') {
            token = telegramTokenRef.current?.value || '';
            webhookUrl = ''; // Backend will fetch the actual webhook URL
          } else {
            // For Telegram Business, use username as URL and leave token empty
            const username = telegramBusinessUsernameRef.current?.value || '';
            // Ensure username starts with @
            const formattedUsername = username.startsWith('@') ? username : `@${username}`;
            token = ''; // Leave token empty for business
            webhookUrl = formattedUsername; // Special case: Use username as URL for Telegram Business
          }
          break;
        case 'web':
          domain = webDomainRef.current?.value || '';
          webhookUrl = ''; // Backend will fetch the actual webhook URL
          break;
        default:
          webhookUrl = '';
      }

      // Check validation before proceeding - handle Telegram Business first
      if (platform === 'telegram' && telegramConnectionType === 'business') {
        // For Telegram Business, check username instead of token
        const username = telegramBusinessUsernameRef.current?.value || '';
        if (!username || username.trim() === '') {
          // Clear modal state and show error overlay
          setShowConnectConfirmation({ show: false, platform: null });
          setConnectConfirmationLoading(false);
          showError(t('enter_valid_telegram_username'));
          setIsConnecting(false);

          // Auto-dismiss error message after 3 seconds
          setTimeout(() => {
            hideOverlay();
          }, 3000);

          return;
        }
      } else if (isWebPlatform) {
        // Check domain for web platforms
        if (!domain || domain.trim() === '') {
          // Clear modal state and show error overlay
          setShowConnectConfirmation({ show: false, platform: null });
          setConnectConfirmationLoading(false);
          showError(t('enter_valid_domain').replace('{platform}', platform));
          setIsConnecting(false);

          // Auto-dismiss error message after 3 seconds
          setTimeout(() => {
            hideOverlay();
          }, 1500);

          return;
        }
      } else {
        // For all other platforms (including Telegram Bot), check token
        if (!token || token.trim() === '') {
          // Clear modal state and show error overlay
          setShowConnectConfirmation({ show: false, platform: null });
          setConnectConfirmationLoading(false);
          showError(t('enter_valid_token').replace('{platform}', platform));
          setIsConnecting(false);

          // Auto-dismiss error message after 3 seconds
          setTimeout(() => {
            hideOverlay();
          }, 3000);

          return;
        }
      }

      // Set connecting platform
      setConnectingPlatform(platform);

      // Prepare request body
      let platformType = platform;

      // For Telegram Business, use telegram_biz as type
      if (platform === 'telegram' && telegramConnectionType === 'business') {
        platformType = 'telegram_biz';
      }

      // For Instagram, append the page ID to the type parameter if available
      if (platform === 'instagram' && instagramIdRef.current?.value) {
        platformType = `${platform}/${instagramIdRef.current.value}`;
      }

      // For WhatsApp, append the ID to the type parameter if available
      /* if (platform === 'whatsapp' && whatsappIdRef.current?.value) {
        platformType = `${platform}/${whatsappIdRef.current.value}`;
      } */

      const requestBody: any = {
        webhook_url: webhookUrl,
        token: isWebPlatform ? domain : token,
        type: platformType
      };

      // For Telegram Business, the webhook_url contains the username and token is empty
      // No additional fields needed as the username is already in webhook_url

      // Send to API endpoint
      const response = await fetch('/api/platform/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      // Wait for API call to complete - no loading overlay shown

      // Wait for a minimum of 1 second for UX purposes
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Parse the response
      const responseData = await response.json();

      if (!response.ok) {
        // Map error codes to translation keys
        let errorKey = 'failed_connect_platform'; // Default error key

        if (responseData.code) {
          switch (responseData.code) {
            case 'INVALID_TOKEN':
              errorKey = 'invalid_token_error';
              break;
            case 'ALREADY_USE_FOR_TRIAL':
              errorKey = 'already_use_for_trial_error';
              break;
            case 'STILL_CONNECTED':
              errorKey = 'still_connected_error';
              break;
            case 'CONNECTION_ERROR':
              errorKey = 'connection_error';
              break;
            // Keep the default for unknown error codes
          }
        }

        // Clear modal state and show error overlay
        setShowConnectConfirmation({ show: false, platform: null });
        setConnectConfirmationLoading(false);
        // Translate the error message using the appropriate key
        showError(t(errorKey).replace('{platform}', platform));
        setIsConnecting(false);

        // Auto-dismiss error message after 5 seconds
        setTimeout(() => {
          hideOverlay();
        }, 5000);

        return;
      }

      // Make sure it's a success (handle both old and new response formats)
      if (responseData.status === 'error' || !responseData.success) {
        // Clear modal state and show error overlay
        setShowConnectConfirmation({ show: false, platform: null });
        setConnectConfirmationLoading(false);
        showError(t('unexpected_server_response').replace('{platform}', platform));
        setIsConnecting(false);

        // Auto-dismiss error message after 3 seconds
        setTimeout(() => {
          hideOverlay();
        }, 3000);

        return;
      }

      // Clear modal state and show success overlay
      setShowConnectConfirmation({ show: false, platform: null });
      setConnectConfirmationLoading(false);
      showSuccess(t('successfully_connected').replace('{platform}', platform));
      setIsConnecting(false);

      // Refetch connection data to update UI
      setTimeout(() => {
        fetchConnectionData();
      }, 1500);

    } catch (error) {
      console.error(`Error connecting ${platform}:`, error);
      // Clear modal state if still open
      setShowConnectConfirmation({ show: false, platform: null });
      setConnectConfirmationLoading(false);
      showError(t('failed_connect_platform').replace('{platform}', platform));
      setIsConnecting(false);

      // Clear error message after 3 seconds
      setTimeout(() => {
        hideOverlay();
      }, 3000);
    } finally {
      // Reset connecting platform and loading state
      setConnectingPlatform(null);
      setConnectConfirmationLoading(false);
    }
  };

  // Don't render anything server-side
  if (!isClient) return null

  return (
    <div className={`${themeConfig.pageBackground} pb-16`}>
      {/* Conditional background effects based on theme */}
      {themeConfig.backgroundEffects}

      {/* Modern Glassmorphism Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${themeConfig.card} rounded-2xl px-4 py-3 border ${themeConfig.border} transition-all duration-300 overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >

            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <LinkButton 
                href="/dashboard" 
                variant="ghost" 
                className="p-0 hover:bg-transparent active:scale-95 focus:outline-none focus:ring-0 focus:border-none"
              >
                <Image
                  src={themeConfig.logo}
                  alt="Chhlat Bot"
                  width={32}
                  height={32}
                  className="h-8 w-auto transition-transform duration-300 hover:scale-105"
                />
              </LinkButton>
            </div>
          </div>
        </div>
      </header>

      {/* Status Overlay - Unified error and success messages */}
      <UpdateStatusOverlay
        updateStatus={overlayStatus}
        updateProgress={0}
        updateMessage={overlayMessage}
        onClose={() => setOverlayStatus('idle')}
        hideProgress={true}
      />


      {/* Connect Confirmation Modal */}
      <UpdateConfirmationModal
        showConfirmation={showConnectConfirmation.show}
        onCancel={() => {
          setShowConnectConfirmation({ show: false, platform: null });
          setConnectConfirmationLoading(false);
        }}
        onConfirm={() => handleConnect(showConnectConfirmation.platform!)}
        title={t('confirm_connection')}
        message={showConnectConfirmation.platform === 'web'
          ? t('confirm_connect_web').replace('{platform}', showConnectConfirmation.platform || '')
          : t('confirm_connect_platform').replace('{platform}', showConnectConfirmation.platform || '')}
        isLoading={connectConfirmationLoading}
      />

      {/* Enable Platform Confirmation Modal */}
      <UpdateConfirmationModal
        showConfirmation={showEnableConfirmation.show}
        onCancel={() => {
          setShowEnableConfirmation({ show: false, platform: null });
          setEnableConfirmationLoading(false);
        }}
        onConfirm={() => handleTogglePlatformStatus(showEnableConfirmation.platform!)}
        title={t('confirm_status_change')}
        message={t('confirm_action_platform').replace('{action}', t('enable')).replace('{platform}', showEnableConfirmation.platform || '')}
        isLoading={enableConfirmationLoading}
      />

      {/* Disable Platform Confirmation Modal */}
      <CancelConfirmationModal
        showCancelConfirmation={showDisableConfirmation.show}
        onKeepEditing={() => {
          setShowDisableConfirmation({ show: false, platform: null });
          setDisableConfirmationLoading(false);
        }}
        onConfirmDiscard={() => handleTogglePlatformStatus(showDisableConfirmation.platform!)}
        isLoading={disableConfirmationLoading}
      />

      <div className="flex-grow container mx-auto px-4 py-2">
        {/* Animation commented out as requested */}
        <div>
          {isLoading ? (
            <ConnectPageSkeleton />
          ) : (
            <>
              <DashboardHeader backHref="/dashboard" titleKey="connect_accounts" />

          {/* Connected Platforms Section - Moved to the top */}
          <div
            className={`relative ${themeConfig.card} rounded-2xl p-6 mb-6 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}>
            <div className="relative z-10">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h2 className={`text-xl font-bold font-title ${themeConfig.text}`}>
                  {t('connected_platforms')} ({isDataReady() ? `${Object.values(connectedPlatforms).filter(Boolean).length}/${getConnectionLimit()}` : '...'})
                </h2>
                {/* <p className={`${themeConfig.textSecondary} text-sm font-body mt-1`}>
                  {t('manage_connected_platforms')} ({Object.values(connectedPlatforms).filter(Boolean).length}/{connectionsLimit})
                </p> */}
              </div>
              {Object.values(connectedPlatforms).filter(Boolean).length > 0 && (
                <Link href="/dashboard/connect/editConnection" className={`${themeConfig.textSecondary} ${themeConfig.secondCardHover} px-3 py-1 rounded transition-colors text-sm font-body`}>
                  {t('edit')}
                </Link>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
              <PlatformCard
                platform="facebook"
                name={t('facebook_messenger')}
                icon={<FaFacebookMessenger size={18} className="text-blue-500" />}
                displayName={credentials?.fb_name || ''}
                isConnected={connectedPlatforms.facebook}
                isEnabled={platformStatus.facebook}
                onToggle={initiateTogglePlatformStatus}
                backgroundColor={themeConfig.secondCard}
              />

              <PlatformCard
                platform="instagram"
                name={t('instagram')}
                icon={<FaInstagram size={18} className="text-pink-500" />}
                displayName={credentials?.ig_name || ''}
                isConnected={connectedPlatforms.instagram}
                isEnabled={platformStatus.instagram}
                onToggle={initiateTogglePlatformStatus}
                backgroundColor={themeConfig.secondCard}
              />

              <PlatformCard
                platform="telegram"
                name={`${t('telegram')} (${credentials?.tg_connection_type || 'Bot'})`}
                icon={<FaPaperPlane size={16} className="text-blue-400" />}
                displayName={credentials?.tg_name || ''}
                isConnected={connectedPlatforms.telegram}
                isEnabled={platformStatus.telegram}
                onToggle={initiateTogglePlatformStatus}
                backgroundColor={themeConfig.secondCard}
              />

              <PlatformCard
                platform="web"
                name={t('web_api')}
                icon={<FaGlobe size={18} className="text-purple-500" />}
                displayName={credentials?.web_domain || ''}
                isConnected={connectedPlatforms.web}
                isEnabled={platformStatus.web}
                onToggle={initiateTogglePlatformStatus}
              />

              {/* No connected platforms message */}
              {Object.values(connectedPlatforms).filter(Boolean).length === 0 && (
                <div className={`col-span-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg p-6 text-center`}>
                  <p className={`${themeConfig.textSecondary} font-body`}>{t('no_platforms_connected')}</p>
                </div>
              )}
            </div>
            </div>
          </div>

          {/* Platform Selector Dropdown */}
          <div
            className={`relative ${themeConfig.card} rounded-2xl p-6 mb-6 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >
            <div className="relative z-10">
            <h2 className={`text-xl font-bold mb-4 font-title ${themeConfig.text}`}>{t('select_platform')}</h2>
            {/* <p className={`${themeConfig.textSecondary} mb-6 font-body`}>
              {t('choose_platform_details')}
            </p> */}

            <div className="relative" ref={dropdownRef}>
              <button
                onClick={() => isDataReady() && Object.values(connectedPlatforms).filter(Boolean).length < getConnectionLimit() && setIsDropdownOpen(!isDropdownOpen)}
                className={`w-full ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg px-4 py-3 ${themeConfig.text} flex items-center justify-between transition-colors ${
                  !isDataReady()
                    ? 'opacity-50 cursor-wait'
                    : Object.values(connectedPlatforms).filter(Boolean).length < getConnectionLimit()
                    ? `${themeConfig.borderHover}`
                    : 'opacity-70 cursor-not-allowed'
                }`}
                disabled={!isDataReady() || Object.values(connectedPlatforms).filter(Boolean).length >= getConnectionLimit()}
              >
                {!isDataReady() ? (
                  <div className={`flex items-center ${themeConfig.textSecondary}`}>
                    <span className="font-body">{t('loading')}...</span>
                  </div>
                ) : Object.values(connectedPlatforms).filter(Boolean).length >= getConnectionLimit() ? (
                  <div className={`flex items-center ${themeConfig.errorText}`}>
                    <span className="font-body">{t('connection_limit_reached')}</span>
                  </div>
                ) : (
                  <>
                    <div className="flex items-center">
                      {platforms.find(p => p.id === selectedPlatform)?.icon}
                      <span className="ml-3 font-body">
                        {platforms.find(p => p.id === selectedPlatform)?.name}
                      </span>
                    </div>
                    <FaChevronDown className={`transition-transform duration-200 ${isDropdownOpen ? 'transform rotate-180' : ''}`} />
                  </>
                )}
              </button>

              {/* Animation commented out as requested */}
              <div
                className={`relative w-full ${themeConfig.card} border ${themeConfig.border} rounded-lg overflow-hidden mt-2 z-10 ${!isDropdownOpen ? 'hidden' : ''}`}
              >
                <ul>
                  {platforms
                    .filter(platform => availablePlatforms.includes(platform.id as Platform))
                    .map((platform) => (
                      <li key={platform.id}>
                        <button
                          onClick={() => !platform.disabled && handleSelectPlatform(platform.id as Platform)}
                          className={`w-full px-4 py-3 text-left flex items-center transition-colors ${
                            platform.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                          } ${
                            selectedPlatform === platform.id 
                              ? `${themeConfig.secondCard} ${themeConfig.text}` 
                              : `${themeConfig.hover} ${themeConfig.text}`
                          }`}
                          disabled={platform.disabled}
                        >
                          {platform.icon}
                          <span className="ml-3 font-body">{platform.name}</span>
                        </button>
                      </li>
                    ))}
                </ul>
              </div>
            </div>
            </div>
          </div>

          {isDataReady() && availablePlatforms.length > 0 && availablePlatforms.some(p => p !== 'tiktok') &&
           Object.values(connectedPlatforms).filter(Boolean).length < getConnectionLimit() && (
            <div
              className={`relative ${themeConfig.card} rounded-2xl p-6 mb-12 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
              style={theme === 'dark' ? {
                boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
              } : {
                boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
              }}
            >
              <div className="relative z-10">
              <div className="flex justify-between items-center mb-4">
                <div>
                  <h2 className={`text-xl font-bold font-title ${themeConfig.text}`}>{t('selected_platform')}</h2>
                  <p className={`${themeConfig.textSecondary} text-sm font-body mt-1`}>
                    {t('connect_platform_message').replace('{platform}', platforms.find(p => p.id === selectedPlatform)?.name || '')}
                  </p>
                </div>
              </div>



            <div className="space-y-6">

              {/* Facebook Messenger */}
              {selectedPlatform === 'facebook' && (
                <PlatformConnectionForm
                  platform="facebook"
                  name={t('facebook_messenger')}
                  icon={<FaFacebookMessenger size={18} className="text-blue-500" />}
                  description={t('copy_webhook_provide_token')}
                  isConnected={connectedPlatforms.facebook}
                  connectingPlatform={connectingPlatform}
                  onConnect={initiateConnect}
                  webhookStates={webhookStates}
                  onCopyWebhook={handleCopyWebhook}
                  tokenRef={facebookTokenRef}
                  onTokenChange={handleTokenChange}
                />
              )}

              {/* Instagram DM */}
              {selectedPlatform === 'instagram' && (
                <PlatformConnectionForm
                  platform="instagram"
                  name={t('instagram_dm')}
                  icon={<FaInstagram size={20} className="text-pink-500" />}
                  description={t('copy_webhook_provide_token_id')}
                  isConnected={connectedPlatforms.instagram}
                  connectingPlatform={connectingPlatform}
                  onConnect={initiateConnect}
                  webhookStates={webhookStates}
                  onCopyWebhook={handleCopyWebhook}
                  tokenRef={instagramTokenRef}
                  secondaryRef={instagramIdRef}
                  onTokenChange={handleTokenChange}
                />
              )}


              {/* Telegram */}
              {selectedPlatform === 'telegram' && (
                <PlatformConnectionForm
                  platform="telegram"
                  name={t('telegram')}
                  icon={<FaPaperPlane size={16} className="text-blue-400" />}
                  description={t('choose_connection_type')}
                  isConnected={connectedPlatforms.telegram}
                  connectingPlatform={connectingPlatform}
                  onConnect={initiateConnect}
                  webhookStates={webhookStates}
                  onCopyWebhook={handleCopyWebhook}
                  onCopyBotName={handleCopyBotName}
                  telegramConnectionType={telegramConnectionType}
                  setTelegramConnectionType={setTelegramConnectionType}
                  tokenRef={telegramConnectionType === 'bot' ? telegramTokenRef : telegramBusinessUsernameRef}
                  onTokenChange={handleTokenChange}
                />
              )}

              {/* Web API */}
              {selectedPlatform === 'web' && (
                <PlatformConnectionForm
                  platform="web"
                  name={t('web_api')}
                  icon={<FaGlobe size={20} className="text-purple-500" />}
                  description={t('coming_soon')}
                  isConnected={connectedPlatforms.web}
                  connectingPlatform={connectingPlatform}
                  onConnect={initiateConnect}
                  webhookStates={webhookStates}
                  onCopyWebhook={handleCopyWebhook}
                  tokenRef={webDomainRef}
                  onTokenChange={handleTokenChange}
                />
              )}

              {/* TikTok */}
              {selectedPlatform === 'tiktok' && (
                <div className={`${themeConfig.card} border ${themeConfig.border} rounded-xl p-6 transition-all`}>
                  <div className="flex flex-col items-center">
                    <div className="flex items-center mb-4">
                      <div className={`w-10 h-10 ${themeConfig.secondCard} rounded-full flex items-center justify-center mr-4 ${themeConfig.text} border ${themeConfig.border}`} style={{ minWidth: '2.5rem' }}>
                        <FaTiktok size={20} />
                      </div>
                      <div>
                        <h3 className={`font-medium ${themeConfig.text} text-lg font-title`}>{t('tiktok')}</h3>
                        <p className={`${themeConfig.textSecondary} text-sm font-body`}>{t('coming_soon')}</p>
                      </div>
                    </div>
                    <p className={`${themeConfig.textMuted} mt-2 font-body`}>{t('more_platforms_coming_soon')}</p>
                  </div>
                </div>
              )}
            </div>
            </div>
          </div>
          )}
            </>
          )}
        </div>
      </div>

      <DashboardFooter />
    </div>
  )
}
